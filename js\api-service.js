/**
 * 数据分析平台 API 服务模块
 * 统一管理所有API请求，包括认证、数据管理、信号处理等
 */

// API配置
const API_CONFIG = {
    BASE_URL: 'https://cugzcfwwhuiq.sealoshzh.site/v1',
    TIMEOUT: 30000, // 增加超时时间
    RETRY_COUNT: 3, // 增加重试次数
    RETRY_DELAY: 1000
};

// 错误码映射
const ERROR_CODES = {
    'AUTH_001': '用户名或密码错误',
    'AUTH_002': '登录已过期，请重新登录',
    'DATA_001': '文件格式不支持',
    'DATA_002': '文件大小超过限制',
    'SIGNAL_001': '信号处理参数错误',
    'EXPORT_001': '导出格式不支持',
    'RATE_001': '请求过于频繁，请稍后再试',
    'VALIDATION_ERROR': '输入参数验证失败',
    'NOT_FOUND': '请求的资源不存在',
    'INTERNAL_ERROR': '服务器内部错误'
};

/**
 * Token管理器
 */
class TokenManager {
    constructor() {
        this.token = localStorage.getItem('auth_token');
        this.refreshToken = localStorage.getItem('refresh_token');
        this.tokenExpiry = localStorage.getItem('token_expiry');
    }

    // 设置Token
    setToken(token, refreshToken = null, expiresIn = 7 * 24 * 60 * 60 * 1000) {
        this.token = token;
        this.refreshToken = refreshToken;
        this.tokenExpiry = Date.now() + expiresIn;
        
        localStorage.setItem('auth_token', token);
        if (refreshToken) {
            localStorage.setItem('refresh_token', refreshToken);
        }
        localStorage.setItem('token_expiry', this.tokenExpiry.toString());
    }

    // 获取Token
    getToken() {
        return this.token;
    }

    // 检查Token是否有效
    isTokenValid() {
        if (!this.token || !this.tokenExpiry) {
            return false;
        }
        return Date.now() < this.tokenExpiry;
    }

    // 清除Token
    clearToken() {
        this.token = null;
        this.refreshToken = null;
        this.tokenExpiry = null;
        
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('token_expiry');
        localStorage.removeItem('currentUser');
        localStorage.removeItem('loginTime');
    }

    // 刷新Token
    async refreshTokenIfNeeded() {
        if (!this.isTokenValid() && this.refreshToken) {
            try {
                const response = await fetch(`${API_CONFIG.BASE_URL}/auth/refresh`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.refreshToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.token) {
                        this.setToken(data.data.token, data.data.refreshToken);
                        return true;
                    }
                }
            } catch (error) {
                console.error('Token刷新失败:', error);
            }
            
            // 刷新失败，清除所有Token
            this.clearToken();
            return false;
        }
        return this.isTokenValid();
    }
}

/**
 * HTTP请求客户端
 */
class HttpClient {
    constructor() {
        this.tokenManager = new TokenManager();
        this.requestQueue = new Map();
    }

    // 创建请求配置
    createRequestConfig(options = {}) {
        const config = {
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // 添加认证头
        const token = this.tokenManager.getToken();
        if (token && options.auth !== false) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }

        return config;
    }

    // 处理响应
    async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        
        // 处理不同类型的响应
        if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            
            if (!response.ok) {
                throw new APIError(
                    data.error?.message || '请求失败',
                    response.status,
                    data.error?.code,
                    data.error?.details
                );
            }
            
            return data;
        } else if (response.ok) {
            // 处理文件下载等非JSON响应
            return response;
        } else {
            throw new APIError('请求失败', response.status);
        }
    }

    // 重试机制
    async retryRequest(url, config, retryCount = 0) {
        try {
            const response = await fetch(url, config);
            return await this.handleResponse(response);
        } catch (error) {
            // 检查是否是CORS错误
            if (this.isCorsError(error)) {
                console.error('CORS错误：后端服务器需要配置跨域访问权限');
                throw new APIError('跨域访问被阻止，请联系管理员配置CORS', 0, 'CORS_ERROR');
            }

            if (retryCount < API_CONFIG.RETRY_COUNT && this.shouldRetry(error)) {
                await this.delay(API_CONFIG.RETRY_DELAY * Math.pow(2, retryCount));
                return this.retryRequest(url, config, retryCount + 1);
            }
            throw error;
        }
    }

    // 检查是否是CORS错误
    isCorsError(error) {
        return error.message && (
            error.message.includes('CORS') ||
            error.message.includes('Access-Control-Allow-Origin') ||
            error.message.includes('ERR_FAILED') ||
            error.message.includes('Failed to fetch')
        );
    }

    // 判断是否应该重试
    shouldRetry(error) {
        return error.status >= 500 || error.status === 429 || !error.status;
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 发送请求
    async request(endpoint, options = {}) {
        const url = `${API_CONFIG.BASE_URL}${endpoint}`;
        
        // 检查并刷新Token
        if (options.auth !== false) {
            const tokenValid = await this.tokenManager.refreshTokenIfNeeded();
            if (!tokenValid && options.requireAuth !== false) {
                throw new APIError('认证失败，请重新登录', 401, 'AUTH_002');
            }
        }

        const config = this.createRequestConfig(options);
        
        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT);
        config.signal = controller.signal;

        try {
            const result = await this.retryRequest(url, config);
            clearTimeout(timeoutId);
            return result;
        } catch (error) {
            clearTimeout(timeoutId);

            if (error.name === 'AbortError') {
                console.error('API请求超时');
                throw new APIError('API服务请求超时，请检查网络连接', 408, 'TIMEOUT_ERROR');
            }
            
            // 处理认证错误
            if (error.status === 401) {
                this.tokenManager.clearToken();
                // 触发登录页面跳转
                if (window.location.pathname !== '/index.html' && window.location.pathname !== '/') {
                    window.location.href = 'index.html';
                }
            }
            
            throw error;
        }
    }

    // GET请求
    async get(endpoint, params = {}, options = {}) {
        // 构建查询参数
        const searchParams = new URLSearchParams();
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                searchParams.append(key, params[key]);
            }
        });

        const queryString = searchParams.toString();
        const fullEndpoint = queryString ? `${endpoint}?${queryString}` : endpoint;

        return this.request(fullEndpoint, { ...options, method: 'GET' });
    }

    // POST请求
    async post(endpoint, data = {}, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(endpoint, data = {}, options = {}) {
        return this.request(endpoint, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'DELETE' });
    }

    // 文件上传
    async upload(endpoint, formData, options = {}) {
        const config = {
            ...options,
            method: 'POST',
            body: formData,
            headers: {
                // 不设置Content-Type，让浏览器自动设置multipart/form-data
                ...options.headers
            }
        };
        
        // 移除Content-Type以支持文件上传
        delete config.headers['Content-Type'];
        
        return this.request(endpoint, config);
    }

    // 文件下载
    async download(endpoint, options = {}) {
        const response = await this.request(endpoint, {
            ...options,
            method: 'GET'
        });

        // 如果是Response对象，直接返回
        if (response instanceof Response) {
            return response;
        }

        return response;
    }
}

/**
 * 自定义API错误类
 */
class APIError extends Error {
    constructor(message, status = 0, code = null, details = null) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.code = code;
        this.details = details;
        this.timestamp = new Date().toISOString();

        // 使用错误码映射获取友好的错误信息
        if (code && ERROR_CODES[code]) {
            this.message = ERROR_CODES[code];
        }
    }

    // 获取用户友好的错误信息
    getUserMessage() {
        if (this.code && ERROR_CODES[this.code]) {
            return ERROR_CODES[this.code];
        }

        switch (this.status) {
            case 400:
                return '请求参数错误';
            case 401:
                return '请先登录';
            case 403:
                return '权限不足';
            case 404:
                return '请求的资源不存在';
            case 408:
                return '请求超时，请重试';
            case 429:
                return '请求过于频繁，请稍后再试';
            case 500:
                return '服务器内部错误';
            default:
                return this.message || '网络错误，请检查网络连接';
        }
    }
}

// 创建全局HTTP客户端实例
const httpClient = new HttpClient();

// 导出API服务
window.APIService = {
    client: httpClient,
    tokenManager: httpClient.tokenManager,
    APIError: APIError,
    ERROR_CODES: ERROR_CODES
};
