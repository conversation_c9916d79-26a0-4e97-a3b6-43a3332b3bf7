// 配置粒子动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 页面加载动画
    setTimeout(function() {
        document.querySelector('.page-loader').classList.add('loaded');
    }, 1500);

    // 修复复选框功能
    const rememberCheckbox = document.getElementById('remember');
    const customCheckbox = document.querySelector('.custom-checkbox');

    if (rememberCheckbox && customCheckbox) {
        // 点击标签时切换复选框状态
        customCheckbox.addEventListener('click', function(e) {
            if (e.target !== rememberCheckbox) {
                e.preventDefault();
                rememberCheckbox.checked = !rememberCheckbox.checked;
                updateCheckboxAppearance();
            }
        });

        // 监听复选框状态变化
        rememberCheckbox.addEventListener('change', updateCheckboxAppearance);

        function updateCheckboxAppearance() {
            if (rememberCheckbox.checked) {
                customCheckbox.classList.add('checked');
            } else {
                customCheckbox.classList.remove('checked');
            }
        }

        // 初始化状态
        updateCheckboxAppearance();
    }
    
    // Particles.js配置
    if(document.getElementById('particles-js')) {
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#2c7be5"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    },
                    "polygon": {
                        "nb_sides": 5
                    }
                },
                "opacity": {
                    "value": 0.4,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 2,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#5499ff",
                    "opacity": 0.3,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 1,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": true,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "grab"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 140,
                        "line_linked": {
                            "opacity": 0.8
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        });
    }

    // 表单验证
    const loginForm = document.getElementById('loginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.querySelector('.password-toggle');

    // 禁用浏览器默认验证
    if (loginForm) {
        loginForm.setAttribute('novalidate', 'true');
    }

    // 为所有输入框添加自定义验证
    [emailInput, passwordInput].forEach(input => {
        if (input) {
            input.addEventListener('invalid', function(e) {
                e.preventDefault(); // 阻止默认验证提示
                showCustomValidation(this);
            });

            input.addEventListener('input', function() {
                if (this.validity.valid || this.value.trim() !== '') {
                    clearError(this);
                }
            });
        }
    });

    // 自定义验证提示函数
    function showCustomValidation(input) {
        const inputGroup = input.closest('.input-group');
        const validationMessage = inputGroup.querySelector('.validation-message');

        let message = '';
        if (input.validity.valueMissing || input.value.trim() === '') {
            message = input.type === 'password' ? '请输入密码' : '请输入邮箱地址';
        } else if (input.validity.tooShort) {
            message = '输入内容太短';
        } else if (input.validity.patternMismatch || input.validity.typeMismatch) {
            message = input.type === 'email' ? '请输入有效的邮箱地址' : '输入格式不正确';
        }

        if (message && validationMessage) {
            validationMessage.textContent = message;
            showError(input, message);
        }
    }
    const passwordStrength = document.querySelector('.password-strength');
    const passwordStrengthBar = document.querySelector('.password-strength-bar');
    const passwordStrengthText = document.querySelector('.password-strength-text');

    // 密码强度检测
    if(passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            if(password.length > 0) {
                passwordStrength.classList.add('show');
                passwordStrengthText.classList.add('show');

                // 简单的密码强度检测
                let strength = 0;
                if(password.length > 6) strength += 25;
                if(password.match(/[a-z]+/)) strength += 25;
                if(password.match(/[A-Z]+/)) strength += 25;
                if(password.match(/[0-9]+/)) strength += 25;
                if(password.match(/[^a-zA-Z0-9]+/)) strength += 25;

                // 设置强度等级
                passwordStrength.className = 'password-strength show';
                passwordStrengthText.className = 'password-strength-text show';
                
                if(strength <= 25) {
                    passwordStrength.classList.add('weak');
                    passwordStrengthText.classList.add('weak');
                    passwordStrengthText.textContent = '密码强度：弱';
                } else if(strength <= 50) {
                    passwordStrength.classList.add('medium');
                    passwordStrengthText.classList.add('medium');
                    passwordStrengthText.textContent = '密码强度：中';
                } else if(strength <= 75) {
                    passwordStrength.classList.add('good');
                    passwordStrengthText.classList.add('good');
                    passwordStrengthText.textContent = '密码强度：好';
                } else {
                    passwordStrength.classList.add('strong');
                    passwordStrengthText.classList.add('strong');
                    passwordStrengthText.textContent = '密码强度：强';
                }
            } else {
                passwordStrength.classList.remove('show');
                passwordStrengthText.classList.remove('show');
            }
        });
    }

    // 密码显示/隐藏切换（当前页面没有此功能，暂时注释）
    /*
    if(passwordToggle) {
        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // 更新图标和标题
            const eyeIcon = this.querySelector('.iconfont');
            if(type === 'text') {
                eyeIcon.classList.remove('icon-eye');
                eyeIcon.classList.add('icon-eye-close');
                this.setAttribute('title', '隐藏密码');
            } else {
                eyeIcon.classList.remove('icon-eye-close');
                eyeIcon.classList.add('icon-eye');
                this.setAttribute('title', '显示密码');
            }
        });
    }
    */

    // 表单输入字段聚焦效果
    const inputGroups = document.querySelectorAll('.input-group');
    inputGroups.forEach(group => {
        const input = group.querySelector('input');
        
        input.addEventListener('focus', function() {
            group.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            group.classList.remove('focused');
            if(this.value.length > 0) {
                group.classList.add('filled');
                validateInput(this);
            } else {
                group.classList.remove('filled');
                group.classList.remove('input-validated');
            }
        });
        
        // 初始化状态
        if(input.value.length > 0) {
            group.classList.add('filled');
        }
    });

    // 简单输入验证
    function validateInput(input) {
        const inputGroup = input.closest('.input-group');
        const value = input.value.trim();

        // 清除之前的错误状态
        clearError(input);

        // 根据输入类型进行验证
        if (input.type === 'email' && input.id === 'email') {
            if (value.length === 0) {
                return; // 空值不显示错误，等待提交时验证
            } else if (!window.AuthAPI?.validateEmail(value)) {
                showError(input, '请输入有效的邮箱地址');
                return;
            } else {
                showSuccess(input);
            }
        } else if (input.type === 'password') {
            if (value.length === 0) {
                return; // 空值不显示错误，等待提交时验证
            } else if (value.length < 6) {
                showError(input, '密码至少需要6个字符');
                return;
            } else {
                showSuccess(input);
                updatePasswordStrength(value);
            }
        }
    }

    // 更新密码强度指示器
    function updatePasswordStrength(password) {
        const strengthBar = document.querySelector('.password-strength-bar');
        const strengthText = document.querySelector('.password-strength-text');

        if (!strengthBar || !strengthText) return;

        let strength = 0;
        let strengthLabel = '弱';
        let strengthColor = '#e63757';

        // 计算密码强度
        if (password.length >= 6) strength += 1;
        if (password.length >= 8) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;

        // 设置强度等级
        if (strength >= 4) {
            strengthLabel = '强';
            strengthColor = '#00d97e';
        } else if (strength >= 2) {
            strengthLabel = '中';
            strengthColor = '#f6c343';
        }

        // 更新UI
        strengthBar.style.width = `${(strength / 5) * 100}%`;
        strengthBar.style.backgroundColor = strengthColor;
        strengthText.textContent = `密码强度：${strengthLabel}`;
        strengthText.style.color = strengthColor;
    }

    // 表单提交处理
    if(loginForm) {
        console.log('登录表单事件监听器已绑定');
        loginForm.addEventListener('submit', async function(e) {
            console.log('登录表单提交事件触发');
            e.preventDefault();

            let valid = true;

            // 验证邮箱
            const email = emailInput.value.trim();
            if(!email) {
                showError(emailInput, '请输入邮箱地址');
                valid = false;
            } else if (!window.AuthAPI?.validateEmail(email)) {
                showError(emailInput, '请输入有效的邮箱地址');
                valid = false;
            } else {
                emailInput.closest('.input-group').classList.remove('input-error');
            }

            // 验证密码
            const password = passwordInput.value.trim();
            if(!password) {
                showError(passwordInput, '请输入密码');
                valid = false;
            } else if (password.length < 6) {
                showError(passwordInput, '密码至少需要6个字符');
                valid = false;
            } else {
                passwordInput.closest('.input-group').classList.remove('input-error');
            }

            if(valid) {
                // 显示加载状态
                const loginBtn = this.querySelector('.login-btn');
                loginBtn.classList.add('btn-loading');
                loginBtn.disabled = true;

                try {
                    console.log('开始调用登录API...');

                    // 调用登录API
                    const result = await window.AuthAPI.login({
                        email: email,
                        password: password
                    });

                    console.log('登录API调用成功:', result);

                    // 显示成功状态
                    showSuccess(emailInput);
                    showSuccess(passwordInput);
                    const successMessage = result.message || '登录成功，正在跳转到数据分析平台...';
                    showMessage(successMessage, 'success');

                    // 跳转到数据分析页面
                    setTimeout(function() {
                        console.log('正在跳转到data.html...');
                        window.location.replace('data.html');
                    }, 1500);

                } catch (error) {
                    console.error('登录失败:', error);

                    // 移除加载状态
                    loginBtn.classList.remove('btn-loading');
                    loginBtn.disabled = false;

                    // 处理不同类型的错误
                    handleLoginError(error, emailInput, passwordInput);
                }
            }
        });
    }

    // 显示错误消息
    function showError(input, message) {
        const inputGroup = input.closest('.input-group');
        const validationMessage = inputGroup.querySelector('.validation-message');
        const validationIcon = inputGroup.querySelector('.validation-icon.error');

        inputGroup.classList.add('input-error');
        inputGroup.classList.remove('success');

        if (validationMessage) {
            validationMessage.textContent = message;
            validationMessage.style.display = 'block';
            setTimeout(() => {
                validationMessage.style.opacity = '1';
                validationMessage.style.transform = 'translateY(0)';
            }, 10);
        }

        // 不对密码输入框显示验证图标
        if (validationIcon && input.type !== 'password' && input.id !== 'password') {
            validationIcon.classList.add('show');
        }

        // 添加震动效果
        input.classList.add('shake-animation');
        setTimeout(() => {
            input.classList.remove('shake-animation');
        }, 600);
    }

    // 清除错误状态
    function clearError(input) {
        const inputGroup = input.closest('.input-group');
        const validationMessage = inputGroup.querySelector('.validation-message');
        const validationIcon = inputGroup.querySelector('.validation-icon');

        inputGroup.classList.remove('input-error');

        if (validationMessage) {
            validationMessage.style.opacity = '0';
            validationMessage.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                validationMessage.style.display = 'none';
            }, 300);
        }

        if (validationIcon) {
            validationIcon.classList.remove('show');
        }
    }

    // 显示成功状态
    function showSuccess(input) {
        const inputGroup = input.closest('.input-group');
        const validationIcon = inputGroup.querySelector('.validation-icon.success');

        inputGroup.classList.add('success');
        inputGroup.classList.remove('input-error');

        // 不对密码输入框显示验证图标
        if (validationIcon && input.type !== 'password' && input.id !== 'password') {
            validationIcon.classList.add('show');
        }
    }

    // 显示消息提示框
    function showMessage(message, type = 'info') {
        // 优先使用全局toast系统
        if (window.toastSystem) {
            window.toastSystem.show(type, '', message, 5000);
            return;
        }

        // 降级到原有的通知系统
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type} notification-top-center`;

        // 设置图标
        let iconClass = '';
        switch(type) {
            case 'success':
                iconClass = 'icon-check';
                break;
            case 'error':
                iconClass = 'icon-close';
                break;
            case 'warning':
                iconClass = 'icon-warning';
                break;
            default:
                iconClass = 'icon-info';
        }

        notification.innerHTML = `
            <div class="notification-icon">
                <span class="iconfont ${iconClass}">
                    <span class="icon ${iconClass}"></span>
                </span>
            </div>
            <div class="notification-content">
                <div class="notification-title">${getNotificationTitle(type)}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <span class="iconfont icon-close">
                    <span class="icon icon-close"></span>
                </span>
            </button>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'fadeOut 0.5s';
                setTimeout(() => {
                    notification.remove();
                }, 500);
            }
        }, 4000);
    }

    // 获取通知标题
    function getNotificationTitle(type) {
        switch(type) {
            case 'success': return '成功';
            case 'error': return '错误';
            case 'warning': return '警告';
            default: return '提示';
        }
    }

    // 处理登录错误
    function handleLoginError(error, emailInput, passwordInput) {
        let errorMessage = '';
        let showFieldError = false;

        // 根据错误代码或消息确定具体错误类型
        if (error.code) {
            switch (error.code) {
                case 'AUTH_001':
                case 'INVALID_CREDENTIALS':
                    errorMessage = '邮箱或密码错误，请检查后重试';
                    showFieldError = true;
                    break;
                case 'USER_NOT_FOUND':
                    errorMessage = '该邮箱尚未注册，请先注册账号';
                    showError(emailInput, '该邮箱尚未注册');
                    break;
                case 'ACCOUNT_LOCKED':
                    errorMessage = '账号已被锁定，请联系管理员';
                    break;
                case 'ACCOUNT_DISABLED':
                    errorMessage = '账号已被禁用，请联系管理员';
                    break;
                case 'TOO_MANY_ATTEMPTS':
                    errorMessage = '登录尝试次数过多，请稍后再试';
                    break;
                case 'NETWORK_ERROR':
                    errorMessage = '网络连接异常，请检查网络后重试';
                    break;
                case 'SERVER_ERROR':
                    errorMessage = '服务器暂时不可用，请稍后重试';
                    break;
                default:
                    errorMessage = error.getUserMessage ? error.getUserMessage() : '登录失败，请重试';
            }
        } else {
            // 根据错误消息内容判断
            const message = error.message || error.toString();
            if (message.includes('credentials') || message.includes('password') || message.includes('邮箱') || message.includes('密码')) {
                errorMessage = '邮箱或密码错误，请检查后重试';
                showFieldError = true;
            } else if (message.includes('not found') || message.includes('不存在')) {
                errorMessage = '该邮箱尚未注册，请先注册账号';
                showError(emailInput, '该邮箱尚未注册');
            } else if (message.includes('network') || message.includes('网络')) {
                errorMessage = '网络连接异常，请检查网络后重试';
            } else if (message.includes('timeout') || message.includes('超时')) {
                errorMessage = '请求超时，请稍后重试';
            } else {
                errorMessage = '登录失败，请重试';
            }
        }

        // 显示全局错误消息
        showMessage(errorMessage, 'error');

        // 显示字段级错误
        if (showFieldError) {
            showError(emailInput, '邮箱或密码错误');
            showError(passwordInput, '邮箱或密码错误');
        }

        // 记录错误统计
        if (window.StatsAPI) {
            window.StatsAPI.trackUserAction({
                action: 'login_failed',
                feature: 'authentication',
                metadata: {
                    errorCode: error.code || 'unknown',
                    errorMessage: errorMessage
                }
            }).catch(err => console.warn('统计记录失败:', err));
        }
    }

    // 显示登录错误（保持向后兼容）
    function showLoginError(message) {
        showMessage(message, 'error');
    }


    // 图标字体加载检测和备用方案
    function checkIconFonts() {
        // 检测iconfont字体是否加载成功
        const testElement = document.createElement('span');
        testElement.className = 'iconfont icon-user';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        testElement.style.fontSize = '24px';
        document.body.appendChild(testElement);

        // 检查字体是否正确渲染
        setTimeout(() => {
            const computedStyle = window.getComputedStyle(testElement);
            const fontFamily = computedStyle.fontFamily;

            // 如果字体未正确加载，启用备用方案
            if (!fontFamily.includes('iconfont')) {
                document.querySelectorAll('.iconfont').forEach(icon => {
                    icon.setAttribute('data-fallback', 'true');
                });
            }

            document.body.removeChild(testElement);
        }, 100);
    }

    // 页面加载完成后检查图标字体
    checkIconFonts();
});
